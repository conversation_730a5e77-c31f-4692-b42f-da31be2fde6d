# First we will import the necessary Library

import os
import pandas as pd
import numpy as np
import math
import datetime as dt
import matplotlib.pyplot as plt

# For Evalution we will use these library

from sklearn.metrics import mean_squared_error, mean_absolute_error, explained_variance_score, r2_score
from sklearn.metrics import mean_poisson_deviance, mean_gamma_deviance, accuracy_score
from sklearn.preprocessing import MinMaxScaler

# For model building we will use these library

import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model, Model
from tensorflow.keras.layers import Dense, Dropout, Input
from tensorflow.keras.layers import LSTM
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers import Adam, Nadam


# For PLotting we will use these library

import matplotlib.pyplot as plt
from itertools import cycle
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

"""# 3. Loading Dataset"""

# Function to clean up the price values (remove commas and quotes)
def clean_price(price_str):
    if isinstance(price_str, str):
        return float(price_str.replace('"', '').replace(',', ''))
    return price_str

# Function to process volume data with K, M, B suffixes
def process_volume(vol_str):
    if isinstance(vol_str, str):
        if 'K' in vol_str:
            return float(vol_str.replace('K', '')) * 1000
        elif 'M' in vol_str:
            return float(vol_str.replace('M', '')) * 1000000
        elif 'B' in vol_str:
            return float(vol_str.replace('B', '')) * 1000000000
        else:
            return float(vol_str)
    return vol_str

# Function to load and preprocess data
def load_data():
    # Load our dataset
    maindf = pd.read_csv('Data/Bitcoin Historical Data.csv')

    # Clean numeric columns - they have commas and quotes
    numeric_columns = ['Price', 'Open', 'High', 'Low']
    for col in numeric_columns:
        maindf[col] = maindf[col].apply(clean_price)

    # Handle the 'Vol.' column
    maindf['Volume'] = maindf['Vol.'].apply(process_volume)

    # Convert Date to datetime
    maindf['Date'] = pd.to_datetime(maindf['Date'], format='%m/%d/%Y')

    # Since the data is in reverse chronological order (newest first), sort it chronologically
    maindf = maindf.sort_values('Date')

    print('Total number of days present in the dataset: ', maindf.shape[0])
    print('Total number of fields present in the dataset: ', maindf.shape[1])

    return maindf

# Function to create dataset for LSTM
def create_dataset(dataset, time_step=1):
    dataX, dataY = [], []
    for i in range(len(dataset)-time_step-1):
        a = dataset[i:(i+time_step), 0]
        dataX.append(a)
        dataY.append(dataset[i + time_step, 0])
    return np.array(dataX), np.array(dataY)

# Advanced Multivariate LSTM model architecture (Primary model for training)
def build_advanced_multivariate_lstm_model(window_size, n_features=2):
    """Build advanced LSTM model with functional API supporting multivariate input (Price + Volume)"""
    input1 = Input(shape=(window_size, n_features))
    x = LSTM(units=64, return_sequences=True)(input1)
    x = Dropout(0.2)(x)
    x = LSTM(units=64, return_sequences=True)(x)
    x = Dropout(0.2)(x)
    x = LSTM(units=64)(x)
    x = Dropout(0.2)(x)
    x = Dense(32, activation='relu')(x)
    dnn_output = Dense(1)(x)
    model = Model(inputs=input1, outputs=[dnn_output])

    return model



def evaluate_model(model, X, y, scaler, name=""):
    """Evaluate model performance with various metrics"""
    predictions = model.predict(X)

    # Transform back to original form
    predictions = scaler.inverse_transform(predictions)
    original_y = scaler.inverse_transform(y.reshape(-1,1))

    # Calculate various metrics
    rmse = math.sqrt(mean_squared_error(original_y, predictions))
    mae = mean_absolute_error(original_y, predictions)
    r2 = r2_score(original_y, predictions)

    print(f"\n{name} Evaluation Metrics:")
    print(f"RMSE: {rmse:.2f}")
    print(f"MAE: {mae:.2f}")
    print(f"R2 Score: {r2:.4f}")

    return rmse, mae, r2

def create_enhanced_sequences(data, window_size, n_features=1):
    """Create sequences for training with enhanced preprocessing supporting multiple features"""
    X, y = [], []
    for i in range(window_size, len(data)):
        if n_features == 1:
            X.append(data[i-window_size:i, 0])
            y.append(data[i, 0])
        else:
            X.append(data[i-window_size:i, :])
            y.append(data[i, 0])  # Still predict only price
    return np.array(X), np.array(y)

def create_multivariate_sequences(price_data, volume_data, window_size):
    """Create sequences with both price and volume features"""
    X, y = [], []
    for i in range(window_size, len(price_data)):
        # Combine price and volume features
        price_seq = price_data[i-window_size:i, 0]
        volume_seq = volume_data[i-window_size:i, 0]

        # Stack features: [price_seq, volume_seq]
        feature_seq = np.column_stack([price_seq, volume_seq])
        X.append(feature_seq)
        y.append(price_data[i, 0])

    return np.array(X), np.array(y)

def rolling_window_validation(model_builder, X, y, n_splits=5, test_size=0.2):
    """
    Perform rolling window validation for time series data

    Parameters:
    - model_builder: function that returns a compiled model
    - X, y: training data
    - window_size: size of the rolling window
    - n_splits: number of validation splits
    - test_size: proportion of data for testing in each split

    Returns:
    - scores: list of validation scores for each split
    - predictions: list of predictions for each split
    """
    scores = []
    predictions = []

    total_samples = len(X)
    test_samples = int(total_samples * test_size)

    print(f"\n=== Rolling Window Validation ===")
    print(f"Total samples: {total_samples}")
    print(f"Test samples per split: {test_samples}")
    print(f"Number of splits: {n_splits}")

    for i in range(n_splits):
        print(f"\nSplit {i+1}/{n_splits}")

        # Calculate split indices
        split_end = total_samples - (n_splits - i - 1) * (test_samples // 2)

        # Ensure we have enough data for training
        train_end = split_end - test_samples
        train_start = max(0, train_end - (total_samples // n_splits))

        print(f"Train range: {train_start} to {train_end}")
        print(f"Test range: {train_end} to {split_end}")

        # Split data
        X_train_split = X[train_start:train_end]
        y_train_split = y[train_start:train_end]
        X_test_split = X[train_end:split_end]
        y_test_split = y[train_end:split_end]

        if len(X_train_split) == 0 or len(X_test_split) == 0:
            print(f"Skipping split {i+1} due to insufficient data")
            continue

        print(f"Train shape: {X_train_split.shape}, Test shape: {X_test_split.shape}")

        # Build and train model
        model = model_builder()

        # Early stopping for efficiency
        early_stopping = EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)

        model.fit(
            X_train_split, y_train_split,
            validation_data=(X_test_split, y_test_split),
            epochs=50,  # Reduced for validation
            batch_size=32,
            callbacks=[early_stopping],
            verbose=0
        )

        # Evaluate
        y_pred = model.predict(X_test_split, verbose=0)
        rmse = np.sqrt(mean_squared_error(y_test_split, y_pred))
        scores.append(rmse)
        predictions.append(y_pred)

        print(f"Split {i+1} RMSE: {rmse:.4f}")

    avg_score = np.mean(scores)
    std_score = np.std(scores)
    print(f"\n=== Rolling Window Validation Results ===")
    print(f"Average RMSE: {avg_score:.4f} ± {std_score:.4f}")
    print(f"Individual scores: {[f'{score:.4f}' for score in scores]}")

    return scores, predictions

def ensemble_predict(models, X):
    """Make ensemble predictions using multiple models"""
    predictions = []
    for model in models:
        pred = model.predict(X, verbose=0)
        predictions.append(pred)

    # Average the predictions
    ensemble_pred = np.mean(predictions, axis=0)
    return ensemble_pred

def enhanced_predict_future(model, scaler, data, window_size, future_days, use_ensemble=False, ensemble_models=None):
    """Enhanced future prediction with optional ensemble"""
    predictions = []
    current_batch = data[-window_size:].copy()

    for i in range(future_days):
        # Reshape for prediction
        current_batch_reshaped = current_batch.reshape((1, window_size, 1))

        # Make prediction
        if use_ensemble and ensemble_models:
            pred = ensemble_predict(ensemble_models, current_batch_reshaped)
        else:
            pred = model.predict(current_batch_reshaped, verbose=0)

        predictions.append(pred[0, 0])

        # Update batch for next prediction
        current_batch = np.append(current_batch[1:], pred[0, 0])

    # Transform back to original scale
    predictions_array = np.array(predictions).reshape(-1, 1)
    predictions_scaled = scaler.inverse_transform(predictions_array)

    return predictions_scaled.flatten()

def train_and_save_model():
    # Load the data
    maindf = load_data()

    # Prepare DataFrame with Price and Volume
    closedf = maindf[['Date', 'Price', 'Volume']].copy()
    closedf['Date'] = pd.to_datetime(closedf['Date'])
    closedf = closedf.sort_values(by='Date', ascending=True).reset_index(drop=True)
    closedf['Price'] = closedf['Price'].astype('float64')
    closedf['Volume'] = closedf['Volume'].astype('float64')
    dates = closedf['Date']

    # Test set: latest 3 years (2022-2025) for accuracy calculation
    test_start_date = pd.to_datetime('2022-01-01')
    test_mask = closedf['Date'] >= test_start_date
    test_size = test_mask.sum()

    print(f"=== ADVANCED MULTIVARIATE LSTM TRAINING ===")
    print(f"Data range: {closedf['Date'].min()} to {closedf['Date'].max()}")
    print(f"Test period: {test_start_date} to {closedf['Date'].max()} ({test_size} days)")
    print(f"Training period: {closedf['Date'].min()} to {test_start_date} ({len(closedf) - test_size} days)")

    # Prepare train/test split with multivariate features
    price = closedf['Price']
    volume = closedf['Volume']

    # Separate scalers for price and volume
    price_scaler = MinMaxScaler()
    volume_scaler = MinMaxScaler()

    price_scaler.fit(price.values.reshape(-1,1))
    volume_scaler.fit(volume.values.reshape(-1,1))

    window_size = 60

    print(f"Total data points: {len(price)}")
    print(f"Test size: {test_size}")
    print(f"Training size: {len(price) - test_size}")
    print(f"Window size: {window_size}")
    print(f"Features: Price + Volume (multivariate)")

    # Scalers are fitted and ready for use in data preparation

    # Enhanced data preparation with multivariate features for 3-year test split
    # Training data (before 2022)
    train_mask = closedf['Date'] < test_start_date
    train_price = price[train_mask]
    train_volume = volume[train_mask]
    train_price_scaled = price_scaler.transform(train_price.values.reshape(-1,1))
    train_volume_scaled = volume_scaler.transform(train_volume.values.reshape(-1,1))

    # Test data (2022-2025) - need extra window_size for sequence creation
    test_start_idx = train_mask.sum() - window_size
    test_price = price[test_start_idx:]
    test_volume = volume[test_start_idx:]
    test_price_scaled = price_scaler.transform(test_price.values.reshape(-1,1))
    test_volume_scaled = volume_scaler.transform(test_volume.values.reshape(-1,1))

    # Create multivariate sequences for Advanced LSTM
    X_train_mv, y_train_mv = create_multivariate_sequences(train_price_scaled, train_volume_scaled, window_size)
    X_test_mv, y_test_mv = create_multivariate_sequences(test_price_scaled, test_volume_scaled, window_size)

    # Reshape data for LSTM input
    y_train_mv = np.reshape(y_train_mv, (-1,1))
    y_test_mv = np.reshape(y_test_mv, (-1,1))

    print('Multivariate X_train Shape: ', X_train_mv.shape)
    print('Multivariate y_train Shape: ', y_train_mv.shape)
    print('Multivariate X_test Shape: ', X_test_mv.shape)
    print('Multivariate y_test Shape: ', y_test_mv.shape)

    # Training callbacks
    early_stopping = EarlyStopping(monitor='val_loss', patience=15, restore_best_weights=True)
    reduce_lr = ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=5, min_lr=0.0001)

    # Phase 1: Train Advanced Multivariate LSTM on 3-year test split for accuracy calculation
    print("\n=== PHASE 1: Testing on Latest 3 Years (2022-2025) ===")
    print("Training Advanced Multivariate LSTM Model...")

    test_model = build_advanced_multivariate_lstm_model(window_size, n_features=2)
    test_model.compile(optimizer='adam', loss='mean_squared_error')

    test_history = test_model.fit(
        X_train_mv, y_train_mv,
        validation_data=(X_test_mv, y_test_mv),
        epochs=100,
        batch_size=128,
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )

    # Calculate accuracy on 3-year test set
    print("\n=== CALCULATING ACCURACY ON 3-YEAR TEST SET ===")
    test_predictions = test_model.predict(X_test_mv, verbose=0)

    # Transform back to original scale
    test_predictions_original = price_scaler.inverse_transform(test_predictions)
    y_test_original = price_scaler.inverse_transform(y_test_mv)

    # Calculate comprehensive performance metrics
    from sklearn.metrics import mean_absolute_percentage_error
    test_rmse = np.sqrt(mean_squared_error(y_test_original, test_predictions_original))
    test_mae = mean_absolute_error(y_test_original, test_predictions_original)
    test_r2 = r2_score(y_test_original, test_predictions_original)
    test_mape = mean_absolute_percentage_error(y_test_original, test_predictions_original)
    test_accuracy = (1 - test_mape) * 100  # Convert to percentage

    print(f"\n=== 3-YEAR TEST PERFORMANCE METRICS ===")
    print(f"RMSE: ${test_rmse:.2f}")
    print(f"MAE: ${test_mae:.2f}")
    print(f"R² Score: {test_r2:.4f}")
    print(f"MAPE: {test_mape:.4f}")
    print(f"ACCURACY: {test_accuracy:.2f}%")

    # Save accuracy for frontend display
    model_dir = 'model'
    os.makedirs(model_dir, exist_ok=True)

    accuracy_data = {
        'accuracy_percentage': test_accuracy,
        'rmse': test_rmse,
        'mae': test_mae,
        'r2_score': test_r2,
        'mape': test_mape,
        'test_period': '2022-2025 (3 years)',
        'model_type': 'Advanced Multivariate LSTM'
    }

    import json
    with open(os.path.join(model_dir, 'model_accuracy.json'), 'w') as f:
        json.dump(accuracy_data, f, indent=2)

    # Phase 2: Retrain with full dataset for production model
    print("\n=== PHASE 2: Retraining with Full Dataset ===")
    print("Preparing full dataset for production model...")

    # Use all data for final training
    full_price_scaled = price_scaler.transform(price.values.reshape(-1,1))
    full_volume_scaled = volume_scaler.transform(volume.values.reshape(-1,1))

    # Create sequences from full dataset
    X_full, y_full = create_multivariate_sequences(full_price_scaled, full_volume_scaled, window_size)
    y_full = np.reshape(y_full, (-1,1))

    print(f"Full dataset shape: X={X_full.shape}, y={y_full.shape}")

    # Train final production model
    print("Training final production model with full dataset...")
    final_model = build_advanced_multivariate_lstm_model(window_size, n_features=2)
    final_model.compile(optimizer='adam', loss='mean_squared_error')

    # Use 20% of full data for validation during training
    val_split = 0.2
    final_history = final_model.fit(
        X_full, y_full,
        validation_split=val_split,
        epochs=100,
        batch_size=128,
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )

    # Evaluate final production model performance
    print("\n=== FINAL PRODUCTION MODEL EVALUATION ===")

    # Evaluate on full dataset (for reference)
    final_predictions = final_model.predict(X_full, verbose=0)
    final_predictions_original = price_scaler.inverse_transform(final_predictions)
    y_full_original = price_scaler.inverse_transform(y_full)

    final_rmse = np.sqrt(mean_squared_error(y_full_original, final_predictions_original))
    final_mae = mean_absolute_error(y_full_original, final_predictions_original)
    final_r2 = r2_score(y_full_original, final_predictions_original)
    final_mape = mean_absolute_percentage_error(y_full_original, final_predictions_original)
    final_accuracy = (1 - final_mape) * 100

    print(f"\n=== FINAL PRODUCTION MODEL METRICS (Full Dataset) ===")
    print(f"RMSE: ${final_rmse:.2f}")
    print(f"MAE: ${final_mae:.2f}")
    print(f"R² Score: {final_r2:.4f}")
    print(f"MAPE: {final_mape:.4f}")
    print(f"ACCURACY: {final_accuracy:.2f}%")

    # Save the final production model and scalers to /model folder
    print(f"\n=== SAVING FINAL MODEL TO /model FOLDER ===")

    # Use final_model as the production model
    model = final_model
    history = final_history

    # Save model and scalers to root /model folder
    model_save_path = os.path.join(model_dir, 'bitcoin_lstm_model.keras')
    model.save(model_save_path)
    print(f"Final production model saved to {model_save_path}")

    import joblib
    price_scaler_path = os.path.join(model_dir, 'bitcoin_price_scaler.save')
    volume_scaler_path = os.path.join(model_dir, 'bitcoin_volume_scaler.save')

    joblib.dump(price_scaler, price_scaler_path)
    joblib.dump(volume_scaler, volume_scaler_path)

    print(f"Price scaler saved to {price_scaler_path}")
    print(f"Volume scaler saved to {volume_scaler_path}")

    # Plot loss curves for final model
    loss = history.history['loss']
    val_loss = history.history['val_loss']
    epochs = range(len(loss))
    plt.figure(figsize=(10, 6))
    plt.plot(epochs, loss, 'r', label='Training loss')
    plt.plot(epochs, val_loss, 'b', label='Validation loss')
    plt.title('Advanced Multivariate LSTM - Training and Validation Loss')
    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    plt.legend(loc=0)
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(model_dir, 'loss_curves.png'), dpi=150, bbox_inches='tight')
    plt.close()

    print(f"Loss curves saved to {os.path.join(model_dir, 'loss_curves.png')}")

    # Create comprehensive performance summary
    print(f"\n=== COMPREHENSIVE PERFORMANCE SUMMARY ===")
    print(f"Model Type: Advanced Multivariate LSTM")
    print(f"Features: Price + Volume")
    print(f"Window Size: {window_size}")
    print(f"Training Data: Pre-2022 ({len(X_train_mv)} sequences)")
    print(f"Test Data: 2022-2025 ({len(X_test_mv)} sequences)")
    print(f"Full Dataset: {len(X_full)} sequences")
    print(f"\n3-Year Test Performance:")
    print(f"  RMSE: ${test_rmse:.2f}")
    print(f"  MAE: ${test_mae:.2f}")
    print(f"  R² Score: {test_r2:.4f}")
    print(f"  MAPE: {test_mape:.4f}")
    print(f"  ACCURACY: {test_accuracy:.2f}%")
    print(f"\nFull Dataset Performance:")
    print(f"  RMSE: ${final_rmse:.2f}")
    print(f"  MAE: ${final_mae:.2f}")
    print(f"  R² Score: {final_r2:.4f}")
    print(f"  MAPE: {final_mape:.4f}")
    print(f"  ACCURACY: {final_accuracy:.2f}%")

    # Create comprehensive visualization for 3-year test results
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 12))

    # Plot 1: 3-year test prediction comparison
    test_dates = dates.iloc[-len(y_test_original):]
    ax1.plot(test_dates, y_test_original, color='blue', lw=2, label='Actual Prices', alpha=0.8)
    ax1.plot(test_dates, test_predictions_original, color='red', lw=2, label='Predicted Prices', alpha=0.8)
    ax1.set_title('Advanced Multivariate LSTM - 3-Year Test Performance (2022-2025)', fontsize=14)
    ax1.set_xlabel('Date', fontsize=12)
    ax1.set_ylabel('Price ($)', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', rotation=45)

    # Plot 2: Training history
    ax2.plot(history.history['loss'], label='Training Loss', color='blue')
    ax2.plot(history.history['val_loss'], label='Validation Loss', color='red')
    ax2.set_title('Model Training History')
    ax2.set_xlabel('Epochs')
    ax2.set_ylabel('Loss')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # Plot 3: Prediction accuracy scatter
    ax3.scatter(y_test_original, test_predictions_original, alpha=0.6, color='green')
    ax3.plot([y_test_original.min(), y_test_original.max()],
             [y_test_original.min(), y_test_original.max()], 'r--', lw=2)
    ax3.set_xlabel('Actual Prices ($)')
    ax3.set_ylabel('Predicted Prices ($)')
    ax3.set_title(f'Actual vs Predicted (R² = {test_r2:.4f})')
    ax3.grid(True, alpha=0.3)

    # Plot 4: Residuals analysis
    residuals = y_test_original.flatten() - test_predictions_original.flatten()
    ax4.scatter(range(len(residuals)), residuals, alpha=0.6, color='purple')
    ax4.axhline(y=0, color='red', linestyle='--')
    ax4.set_xlabel('Sample Index')
    ax4.set_ylabel('Residuals ($)')
    ax4.set_title(f'Prediction Residuals (RMSE = ${test_rmse:.2f})')
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(model_dir, 'advanced_lstm_analysis.png'), dpi=150, bbox_inches='tight')
    plt.close()

    print(f"Analysis plots saved to {os.path.join(model_dir, 'advanced_lstm_analysis.png')}")

    # Create additional plot showing price and volume correlation for test period
    plt.figure(figsize=(15, 8))

    # Plot price comparison
    ax1 = plt.subplot(2, 1, 1)
    plt.plot(test_dates, y_test_original, color='blue', label='Actual Price', alpha=0.8)
    plt.plot(test_dates, test_predictions_original, color='red', label='Predicted Price', alpha=0.8)
    plt.title('Advanced Multivariate LSTM - Price Prediction with Volume Analysis (2022-2025)')
    plt.ylabel('Price ($)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tick_params(axis='x', rotation=45)

    # Plot volume for test period
    ax2 = plt.subplot(2, 1, 2)
    test_volume_original = volume_scaler.inverse_transform(test_volume_scaled[-len(y_test_original):])
    plt.plot(test_dates, test_volume_original, color='orange', label='Volume', alpha=0.8)
    plt.ylabel('Volume')
    plt.xlabel('Date')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tick_params(axis='x', rotation=45)

    plt.tight_layout()
    plt.savefig(os.path.join(model_dir, 'price_volume_analysis.png'), dpi=150, bbox_inches='tight')
    plt.close()

    print(f"Price-Volume analysis saved to {os.path.join(model_dir, 'price_volume_analysis.png')}")

    return model, price_scaler, volume_scaler, window_size, closedf, dates

def print_enhancement_summary():
    """Print summary of the Advanced Multivariate LSTM model"""
    print("\n" + "="*80)
    print("ADVANCED MULTIVARIATE LSTM BITCOIN PRICE PREDICTION MODEL")
    print("="*80)
    print("\nModel Features:")
    print("1. ✓ Advanced Multivariate LSTM architecture (3 layers, 64 units each)")
    print("2. ✓ Multivariate input features (Price + Volume)")
    print("3. ✓ 3-year test split for accuracy calculation (2022-2025)")
    print("4. ✓ Full dataset retraining for production model")
    print("5. ✓ Comprehensive performance metrics reporting")
    print("6. ✓ Enhanced data preprocessing with sequence creation")
    print("7. ✓ Early stopping and learning rate reduction callbacks")
    print("8. ✓ Advanced visualization with multiple analysis plots")
    print("9. ✓ Consolidated model and image storage in /model folder")
    print("10. ✓ Accuracy percentage calculation for frontend display")
    print("\nKey Improvements:")
    print("- Single, optimized LSTM model (no ensemble complexity)")
    print("- Volume data integration for enhanced prediction accuracy")
    print("- 3-year test period for realistic accuracy assessment")
    print("- Full dataset utilization for final production model")
    print("- Comprehensive performance metrics (RMSE, MAE, R², MAPE, Accuracy%)")
    print("\nTraining Strategy:")
    print("- Phase 1: Train on pre-2022 data, test on 2022-2025 (3 years)")
    print("- Calculate accuracy percentage on 3-year test set")
    print("- Phase 2: Retrain with full dataset for production model")
    print("- Save all outputs to single /model folder")
    print("- Generate comprehensive performance reports")
    print("\nExpected Benefits:")
    print("- Higher accuracy through volume data integration")
    print("- Realistic performance assessment with 3-year test")
    print("- Simplified architecture for better maintainability")
    print("- Comprehensive metrics for user confidence")
    print("- Consolidated file structure for easier deployment")
    print("="*80)

# Main execution
if __name__ == "__main__":
    print_enhancement_summary()
    print("\nStarting Advanced Multivariate LSTM training process...")

    # Train the advanced model
    train_and_save_model()

    print("\n" + "="*80)
    print("ADVANCED MULTIVARIATE LSTM TRAINING COMPLETED SUCCESSFULLY!")
    print("="*80)
    print("\nModel files saved to /model folder:")
    print("- Production model: model/bitcoin_lstm_model.keras")
    print("- Price scaler: model/bitcoin_price_scaler.save")
    print("- Volume scaler: model/bitcoin_volume_scaler.save")
    print("- Model accuracy: model/model_accuracy.json")
    print("- Training curves: model/loss_curves.png")
    print("- Analysis plots: model/advanced_lstm_analysis.png")
    print("- Price-Volume analysis: model/price_volume_analysis.png")
    print("\nThe Advanced Multivariate LSTM model is ready for production use.")
    print("Accuracy percentage is available for frontend display.")
    print("="*80)